"""Device Module DICOM validation - PS3.3 C.7.6.12

Validates DICOM Device Module according to PS3.3 specification including:
- Type 1 required elements validation
- Type 2C conditional requirements (Device Diameter Units when Device Diameter present)
- Code Sequence Macro attributes validation in device sequence items
- Enumerated values compliance for Device Diameter Units
- Comprehensive error reporting with DICOM tag references and resolution guidance
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.equipment_enums import DeviceDiameterUnits


class DeviceValidator(BaseValidator):
    """Validator for DICOM Device Module (PS3.3 C.7.6.12).
    
    Validates Device Module requirements including:
    - Type 1 Device Sequence presence and structure
    - Code Sequence Macro attributes in each device item
    - Type 2C conditional requirements (Device Diameter Units when Device Diameter present)
    - Enumerated value compliance for Device Diameter Units
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Device Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        DeviceValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            DeviceValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            DeviceValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            DeviceValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements.
        
        Device Sequence (0050,0010) is Type 1 - must be present and non-empty.
        """
        
        # Type 1: Device Sequence (0050,0010)
        if not hasattr(dataset, 'DeviceSequence'):
            result.add_error(
                "Device Sequence (0050,0010) is required (Type 1). "
                "Use DeviceModule.from_required_elements() with at least one device item."
            )
        elif not dataset.DeviceSequence:
            result.add_error(
                "Device Sequence (0050,0010) cannot be empty (Type 1). "
                "At least one device must be specified using DeviceModule.create_device_item()."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements.
        
        Device Diameter Units (0050,0017) is Type 2C - required if Device Diameter 
        (0050,0016) is present in any device item.
        """
        
        # Type 2C: Device Diameter Units required if Device Diameter is present
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            device_diameter = getattr(item, 'DeviceDiameter', None)
            device_diameter_units = getattr(item, 'DeviceDiameterUnits', None)
            
            if device_diameter is not None and not device_diameter_units:
                result.add_error(
                    f"Device Sequence item {i}: Device Diameter Units (0050,0017) is required "
                    f"when Device Diameter (0050,0016) is present (Type 2C). "
                    f"Valid units: FR (French), GA (Gauge), IN (Inch), MM (Millimeter)"
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications.
        
        Device Diameter Units (0050,0017) must be one of the defined terms:
        FR (French), GA (Gauge), IN (Inch), MM (Millimeter)
        """
        
        # Device Diameter Units (0050,0017) enumerated value validation
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            diameter_units = getattr(item, 'DeviceDiameterUnits', None)
            if diameter_units:
                valid_units = [unit.value for unit in DeviceDiameterUnits]
                if diameter_units not in valid_units:
                    result.add_error(
                        f"Device Sequence item {i}: Device Diameter Units (0050,0017) "
                        f"has invalid value '{diameter_units}'. "
                        f"Valid values are: {', '.join(valid_units)} "
                        f"(FR=French, GA=Gauge, IN=Inch, MM=Millimeter)"
                    )
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Device Sequence structure and Code Sequence Macro requirements.
        
        Each device item must:
        - Be a pydicom Dataset object
        - Include Code Sequence Macro attributes (CodeValue, CodingSchemeDesignator, CodeMeaning)
        - Follow DICOM PS3.3 Table C.7-18 specifications
        """
        
        # Device Sequence - validate sequence structure and Code Sequence Macro
        device_seq = getattr(dataset, 'DeviceSequence', [])
        for i, item in enumerate(device_seq):
            # Validate that sequence items are Dataset objects
            if not isinstance(item, Dataset):
                result.add_error(
                    f"Device Sequence item {i}: Must be a pydicom Dataset object, "
                    f"got {type(item).__name__}. Use DeviceModule.create_device_item() to create items."
                )
                continue
                
            # Code Sequence Macro attributes (from Table 8.8-1)
            code_value = getattr(item, 'CodeValue', None)
            coding_scheme = getattr(item, 'CodingSchemeDesignator', None) 
            code_meaning = getattr(item, 'CodeMeaning', None)
            
            if not code_value:
                result.add_error(
                    f"Device Sequence item {i}: Code Value (0008,0100) is required "
                    f"for Code Sequence Macro. Specify device type code."
                )
            if not coding_scheme:
                result.add_error(
                    f"Device Sequence item {i}: Coding Scheme Designator (0008,0102) is required "
                    f"for Code Sequence Macro. Use standard like 'SRT' for SNOMED CT."
                )
            if not code_meaning:
                result.add_error(
                    f"Device Sequence item {i}: Code Meaning (0008,0104) is required "
                    f"for Code Sequence Macro. Provide human-readable device description."
                )
