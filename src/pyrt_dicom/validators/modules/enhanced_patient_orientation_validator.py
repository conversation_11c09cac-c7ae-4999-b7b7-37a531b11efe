"""Enhanced Patient Orientation Module DICOM validation - PS3.3 C.7.6.30"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class EnhancedPatientOrientationValidator(BaseValidator):
    """Validator for DICOM Enhanced Patient Orientation Module (PS3.3 C.7.6.30)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Enhanced Patient Orientation Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        EnhancedPatientOrientationValidator._validate_required_elements(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            EnhancedPatientOrientationValidator._validate_sequence_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            EnhancedPatientOrientationValidator._validate_enumerated_values(dataset, result)
        
        # Validate cross-sequence logical consistency
        EnhancedPatientOrientationValidator._validate_cross_sequence_consistency(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        
        # Patient Orientation Code Sequence (0054,0410) - Type 1
        if not hasattr(dataset, 'PatientOrientationCodeSequence') or not dataset.PatientOrientationCodeSequence:
            result.add_error("Patient Orientation Code Sequence (0054,0410) is required (Type 1)")
        
        # Patient Orientation Modifier Code Sequence (0054,0412) - Type 1
        if not hasattr(dataset, 'PatientOrientationModifierCodeSequence') or not dataset.PatientOrientationModifierCodeSequence:
            result.add_error("Patient Orientation Modifier Code Sequence (0054,0412) is required (Type 1)")
        
        # Patient Equipment Relationship Code Sequence (3010,0030) - Type 1
        if not hasattr(dataset, 'PatientEquipmentRelationshipCodeSequence') or not dataset.PatientEquipmentRelationshipCodeSequence:
            result.add_error("Patient Equipment Relationship Code Sequence (3010,0030) is required (Type 1)")
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure requirements."""
        
        # Validate Patient Orientation Code Sequence structure
        orientation_seq = getattr(dataset, 'PatientOrientationCodeSequence', [])
        for i, item in enumerate(orientation_seq):
            if not item.get('CodeValue'):
                result.add_error(
                    f"Patient Orientation Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Patient Orientation Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result.add_error(
                    f"Patient Orientation Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        # Validate Patient Orientation Modifier Code Sequence structure
        modifier_seq = getattr(dataset, 'PatientOrientationModifierCodeSequence', [])
        for i, item in enumerate(modifier_seq):
            if not item.get('CodeValue'):
                result.add_error(
                    f"Patient Orientation Modifier Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Patient Orientation Modifier Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result.add_error(
                    f"Patient Orientation Modifier Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
        
        # Validate Patient Equipment Relationship Code Sequence structure
        equipment_seq = getattr(dataset, 'PatientEquipmentRelationshipCodeSequence', [])
        for i, item in enumerate(equipment_seq):
            if not item.get('CodeValue'):
                result.add_error(
                    f"Patient Equipment Relationship Code Sequence item {i}: Code Value (0008,0100) is required"
                )
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Patient Equipment Relationship Code Sequence item {i}: Coding Scheme Designator (0008,0102) is required"
                )
            if not item.get('CodeMeaning'):
                result.add_error(
                    f"Patient Equipment Relationship Code Sequence item {i}: Code Meaning (0008,0104) is required"
                )
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: ValidationResult) -> None:
        """Validate enumerated values against DICOM specifications."""
        
        # DICOM-defined orientation codes with required coding schemes
        valid_orientation_codes = {
            "102538003": "SCT",  # recumbent
            "C86043": "NCIt",    # erect
            "102539006": "SCT"   # semi-erect
        }
        
        # DICOM-defined modifier codes with required coding schemes
        valid_modifier_codes = {
            "40199007": "SCT",   # supine
            "1240000": "SCT",    # prone
            "10904000": "SCT",   # standing
            "33586001": "SCT"    # sitting
        }
        
        # DICOM-defined equipment relationship codes with required coding schemes
        valid_equipment_codes = {
            "102540008": "SCT",  # headfirst
            "102541007": "SCT",  # feetfirst
            "126833": "DCM"      # anterior first
        }
        
        # Validate Patient Orientation Code Sequence values and coding schemes
        orientation_seq = getattr(dataset, 'PatientOrientationCodeSequence', [])
        for i, item in enumerate(orientation_seq):
            code_value = item.get('CodeValue', '')
            coding_scheme = item.get('CodingSchemeDesignator', '')
            
            if code_value:
                if code_value not in valid_orientation_codes:
                    result.add_warning(
                        f"Patient Orientation Code Sequence item {i}: Code Value '{code_value}' "
                        f"is not a DICOM-defined orientation code"
                    )
                elif coding_scheme != valid_orientation_codes[code_value]:
                    result.add_error(
                        f"Patient Orientation Code Sequence item {i}: Code Value '{code_value}' "
                        f"requires Coding Scheme Designator '{valid_orientation_codes[code_value]}', "
                        f"got '{coding_scheme}'"
                    )
        
        # Validate Patient Orientation Modifier Code Sequence values and coding schemes
        modifier_seq = getattr(dataset, 'PatientOrientationModifierCodeSequence', [])
        for i, item in enumerate(modifier_seq):
            code_value = item.get('CodeValue', '')
            coding_scheme = item.get('CodingSchemeDesignator', '')
            
            if code_value:
                if code_value not in valid_modifier_codes:
                    result.add_warning(
                        f"Patient Orientation Modifier Code Sequence item {i}: Code Value '{code_value}' "
                        f"is not a DICOM-defined modifier code"
                    )
                elif coding_scheme != valid_modifier_codes[code_value]:
                    result.add_error(
                        f"Patient Orientation Modifier Code Sequence item {i}: Code Value '{code_value}' "
                        f"requires Coding Scheme Designator '{valid_modifier_codes[code_value]}', "
                        f"got '{coding_scheme}'"
                    )
        
        # Validate Patient Equipment Relationship Code Sequence values and coding schemes
        equipment_seq = getattr(dataset, 'PatientEquipmentRelationshipCodeSequence', [])
        for i, item in enumerate(equipment_seq):
            code_value = item.get('CodeValue', '')
            coding_scheme = item.get('CodingSchemeDesignator', '')
            
            if code_value:
                if code_value not in valid_equipment_codes:
                    result.add_warning(
                        f"Patient Equipment Relationship Code Sequence item {i}: Code Value '{code_value}' "
                        f"is not a DICOM-defined equipment relationship code"
                    )
                elif coding_scheme != valid_equipment_codes[code_value]:
                    result.add_error(
                        f"Patient Equipment Relationship Code Sequence item {i}: Code Value '{code_value}' "
                        f"requires Coding Scheme Designator '{valid_equipment_codes[code_value]}', "
                        f"got '{coding_scheme}'"
                    )
    
    @staticmethod
    def _validate_cross_sequence_consistency(dataset: Dataset, result: ValidationResult) -> None:
        """Validate logical consistency between orientation, modifier, and equipment sequences."""
        
        orientation_seq = getattr(dataset, 'PatientOrientationCodeSequence', [])
        modifier_seq = getattr(dataset, 'PatientOrientationModifierCodeSequence', [])
        equipment_seq = getattr(dataset, 'PatientEquipmentRelationshipCodeSequence', [])
        
        # Extract code values
        orientation_codes = [item.get('CodeValue', '') for item in orientation_seq]
        modifier_codes = [item.get('CodeValue', '') for item in modifier_seq]
        equipment_codes = [item.get('CodeValue', '') for item in equipment_seq]
        
        # Check for mutually exclusive orientations
        if '102538003' in orientation_codes and 'C86043' in orientation_codes:
            result.add_error(
                "Invalid combination: Patient cannot be both recumbent (102538003) and erect (C86043) "
                "simultaneously. These orientations are mutually exclusive."
            )
        
        if '102538003' in orientation_codes and '102539006' in orientation_codes:
            result.add_error(
                "Invalid combination: Patient cannot be both recumbent (102538003) and semi-erect (102539006) "
                "simultaneously. These orientations are mutually exclusive."
            )
        
        if 'C86043' in orientation_codes and '102539006' in orientation_codes:
            result.add_error(
                "Invalid combination: Patient cannot be both erect (C86043) and semi-erect (102539006) "
                "simultaneously. These orientations are mutually exclusive."
            )
        
        # Validate orientation-modifier compatibility
        if '102538003' in orientation_codes:  # recumbent
            # Recumbent patients should have supine or prone modifiers
            valid_recumbent_modifiers = ['40199007', '1240000']  # supine, prone
            if not any(code in valid_recumbent_modifiers for code in modifier_codes):
                result.add_error(
                    "Invalid combination: Recumbent orientation (102538003) requires compatible modifier "
                    "such as supine (40199007) or prone (1240000). Found modifiers: " +
                    ", ".join(modifier_codes) if modifier_codes else "none"
                )
        
        if 'C86043' in orientation_codes:  # erect
            # Erect patients should have standing or sitting modifiers
            valid_erect_modifiers = ['10904000', '33586001']  # standing, sitting
            if not any(code in valid_erect_modifiers for code in modifier_codes):
                result.add_error(
                    "Invalid combination: Erect orientation (C86043) requires compatible modifier "
                    "such as standing (10904000) or sitting (33586001). Found modifiers: " +
                    ", ".join(modifier_codes) if modifier_codes else "none"
                )

        if '102539006' in orientation_codes:  # semi-erect
            # Semi-erect patients can have various modifiers but should be consistent with partial positioning
            # According to DICOM PS3.3, semi-erect refers to anatomy partway between erect and recumbent (e.g., 45 degrees)
            # This could be compatible with sitting or other intermediate positions
            valid_semi_erect_modifiers = ['33586001']  # sitting is most common for semi-erect
            if not any(code in valid_semi_erect_modifiers for code in modifier_codes):
                result.add_warning(
                    "Semi-erect orientation (102539006) typically pairs with sitting (33586001) modifier. "
                    f"Found modifiers: {', '.join(modifier_codes) if modifier_codes else 'none'}. "
                    "Please verify this combination is clinically appropriate."
                )
        
        # Check for incompatible modifier combinations
        if '40199007' in modifier_codes and '1240000' in modifier_codes:  # supine and prone
            result.add_error(
                "Invalid combination: Patient cannot be both supine (40199007) and prone (1240000) "
                "simultaneously. These modifiers are mutually exclusive."
            )
        
        if '10904000' in modifier_codes and '33586001' in modifier_codes:  # standing and sitting
            result.add_error(
                "Invalid combination: Patient cannot be both standing (10904000) and sitting (33586001) "
                "simultaneously. These modifiers are mutually exclusive."
            )
        
        # Validate known valid combinations from DICOM examples
        EnhancedPatientOrientationValidator._validate_known_combinations(
            orientation_codes, modifier_codes, equipment_codes, result
        )
    
    @staticmethod
    def _validate_known_combinations(orientation_codes: list[str], modifier_codes: list[str], 
                                   equipment_codes: list[str], result: ValidationResult) -> None:
        """Validate against known valid combinations from DICOM PS3.3 Table C.********-1."""
        
        # Known valid combinations from DICOM examples
        valid_combinations = [
            # Conventional CT: recumbent + supine + headfirst
            (['102538003'], ['40199007'], ['102540008']),
            # Standing CT: erect + standing + headfirst  
            (['C86043'], ['10904000'], ['102540008']),
            # Seated CT: erect + sitting + headfirst
            (['C86043'], ['33586001'], ['102540008']),
            # Breast CT: recumbent + prone + anterior first
            (['102538003'], ['1240000'], ['126833'])
        ]
        
        # Check if current combination matches any known valid combination
        for valid_orientation, valid_modifier, valid_equipment in valid_combinations:
            if (set(orientation_codes) == set(valid_orientation) and
                set(modifier_codes) == set(valid_modifier) and  
                set(equipment_codes) == set(valid_equipment)):
                # Found exact match with a known valid combination
                return
        
        # If we have a complete combination but it doesn't match known valid ones, add a warning
        if orientation_codes and modifier_codes and equipment_codes:
            result.add_warning(
                f"Orientation combination [{', '.join(orientation_codes)}] + "
                f"[{', '.join(modifier_codes)}] + [{', '.join(equipment_codes)}] "
                f"does not match any known valid combinations from DICOM PS3.3 Table C.********-1. "
                f"Please verify this combination is clinically appropriate."
            )
    
    @staticmethod
    def validate_lateral_decubitus_side(side: str, result: ValidationResult) -> None:
        """Validate lateral decubitus side parameter.
        
        Args:
            side: Side parameter for decubitus position
            result: ValidationResult to add errors/warnings to
        """
        valid_sides = ["left", "right"]
        if side.lower() not in valid_sides:
            result.add_error(
                f"Invalid side parameter '{side}' for lateral decubitus orientation. "
                f"Must be one of: {', '.join(valid_sides)}"
            )
