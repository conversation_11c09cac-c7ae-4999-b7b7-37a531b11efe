"""
Test EnhancedPatientOrientationModule functionality.

EnhancedPatientOrientationModule implements DICOM PS3.3 C.7.6.30 Enhanced Patient Orientation Module.
Describes patient orientation with respect to gravity and equipment using three sequence attributes.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.modules import EnhancedPatientOrientationModule
from pyrt_dicom.validators import ValidationResult


class TestEnhancedPatientOrientationModule:
    """Test EnhancedPatientOrientationModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )

        # Test using to_dataset() method for new composition-based architecture
        dataset = orientation.to_dataset()
        assert hasattr(dataset, 'PatientOrientationCodeSequence')
        assert hasattr(dataset, 'PatientOrientationModifierCodeSequence')
        assert hasattr(dataset, 'PatientEquipmentRelationshipCodeSequence')
        assert len(dataset.PatientOrientationCodeSequence) == 1
        assert len(dataset.PatientOrientationModifierCodeSequence) == 1
        assert len(dataset.PatientEquipmentRelationshipCodeSequence) == 1

    def test_to_dataset_method(self):
        """Test to_dataset() method returns proper pydicom Dataset."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )

        dataset = orientation.to_dataset()
        assert isinstance(dataset, Dataset)
        assert len(dataset) == 3  # Should have 3 sequences

        # Verify dataset contains expected sequences
        assert hasattr(dataset, 'PatientOrientationCodeSequence')
        assert hasattr(dataset, 'PatientOrientationModifierCodeSequence')
        assert hasattr(dataset, 'PatientEquipmentRelationshipCodeSequence')

        # Verify sequence contents
        assert len(dataset.PatientOrientationCodeSequence) == 1
        assert dataset.PatientOrientationCodeSequence[0].CodeValue == "102538003"

    def test_with_optional_elements_no_arguments(self):
        """Test with_optional_elements with no arguments (valid)."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        # Should not raise any errors
        result = orientation.with_optional_elements()
        assert result is orientation  # Should return self
    
    def test_with_optional_elements_raises_error_with_arguments(self):
        """Test with_optional_elements raises error when arguments provided."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        with pytest.raises(ValueError) as exc_info:
            orientation.with_optional_elements(invalid_param="value")
        
        assert "has no optional elements" in str(exc_info.value)
        assert "invalid_param" in str(exc_info.value)
    
    def test_create_code_sequence_item_required_only(self):
        """Test creating code sequence item with required attributes only."""
        item = EnhancedPatientOrientationModule.create_code_sequence_item(
            code_value="102538003",
            coding_scheme_designator="SCT",
            code_meaning="recumbent"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodeValue == "102538003"
        assert item.CodingSchemeDesignator == "SCT"
        assert item.CodeMeaning == "recumbent"
    
    def test_create_code_sequence_item_with_optional(self):
        """Test creating code sequence item with optional attributes."""
        item = EnhancedPatientOrientationModule.create_code_sequence_item(
            code_value="102538003",
            coding_scheme_designator="SCT",
            code_meaning="recumbent",
            coding_scheme_version="2023-01",
            context_identifier="123",
            context_uid="1.2.3.4"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodeValue == "102538003"
        assert item.CodingSchemeDesignator == "SCT"
        assert item.CodeMeaning == "recumbent"
        assert item.CodingSchemeVersion == "2023-01"
        assert item.ContextIdentifier == "123"
        assert item.ContextUID == "1.2.3.4"
    
    def test_has_orientation_data_property(self):
        """Test has_orientation_data property."""
        # Test with incomplete data
        incomplete_orientation = EnhancedPatientOrientationModule()
        assert not incomplete_orientation.has_orientation_data
        
        # Test with complete data
        complete_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert complete_orientation.has_orientation_data
    
    def test_is_recumbent_property(self):
        """Test is_recumbent property."""
        # Test with recumbent orientation
        recumbent_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert recumbent_orientation.is_recumbent
        
        # Test with different orientation
        other_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="C86043",
                    coding_scheme_designator="NCIt", 
                    code_meaning="erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert not other_orientation.is_recumbent
    
    def test_is_erect_property(self):
        """Test is_erect property."""
        # Test with erect orientation
        erect_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="C86043",
                    coding_scheme_designator="NCIt", 
                    code_meaning="erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert erect_orientation.is_erect
        
        # Test with recumbent orientation
        recumbent_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert not recumbent_orientation.is_erect
    
    def test_is_headfirst_property(self):
        """Test is_headfirst property."""
        # Test with headfirst orientation
        headfirst_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert headfirst_orientation.is_headfirst
        
        # Test with feet-first orientation
        feetfirst_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102541007", 
                    coding_scheme_designator="SCT",
                    code_meaning="feetfirst"
                )
            ]
        )
        assert not feetfirst_orientation.is_headfirst

    def test_is_feetfirst_property(self):
        """Test is_feetfirst property."""
        # Test with feetfirst orientation
        feetfirst_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102541007",
                    coding_scheme_designator="SCT",
                    code_meaning="feetfirst"
                )
            ]
        )
        assert feetfirst_orientation.is_feetfirst

        # Test with headfirst orientation
        headfirst_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert not headfirst_orientation.is_feetfirst

    def test_is_semi_erect_property(self):
        """Test is_semi_erect property."""
        # Test with semi-erect orientation
        semi_erect_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102539006",
                    coding_scheme_designator="SCT",
                    code_meaning="semi-erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="33586001",
                    coding_scheme_designator="SCT",
                    code_meaning="sitting"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert semi_erect_orientation.is_semi_erect

        # Test with erect orientation
        erect_orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="C86043",
                    coding_scheme_designator="NCIt",
                    code_meaning="erect"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="10904000",
                    coding_scheme_designator="SCT",
                    code_meaning="standing"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        assert not erect_orientation.is_semi_erect

    def test_validate_method(self):
        """Test validate method calls validator."""
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT", 
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008", 
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )
        
        assert hasattr(orientation, 'validate')
        assert callable(orientation.validate)
        
        # Test validation result structure
        validation_result = orientation.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)